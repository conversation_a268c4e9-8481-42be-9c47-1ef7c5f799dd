{"permissions": {"allow": ["mcp__ruv-swarm", "mcp__claude-flow", "Bash(claude --version)", "Bash(sudo npm install:*)", "Bash(ln:*)", "Bash(export PATH=\"$HOME/bin:$PATH\")", "Bash(claude-flow --version)", "WebFetch(domain:github.com)", "Bash(npm run build:*)", "Bash(npm run typecheck:*)", "Bash(npm run:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(find:*)", "Bash(rm:*)", "mcp__supabase__list_projects", "mcp__supabase__execute_sql", "mcp__supabase__list_tables", "mcp__supabase__apply_migration", "Bash(supabase status:*)", "<PERSON><PERSON>(pkill:*)", "Bash(git fetch:*)", "Bash(git checkout:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "<PERSON><PERSON>(curl:*)", "Bash(npx supabase:*)", "Bash(git stash:*)", "Bash(grep:*)", "Bash(node:*)", "Bash(if [ -n \"$VITE_GEMINI_API_KEY\" ])", "Bash(then echo \"✅ VITE_GEMINI_API_KEY is set ($#VITE_GEMINI_API_KEY characters)\")", "Bash(else echo \"❌ VITE_GEMINI_API_KEY is not set\")", "Bash(fi)", "Bash(VITE_GEMINI_API_KEY=AIzaSyBHFASKTHXxYSKCfmOt0GhFj68ZfkwFHuM node test_validation_engine.js)", "Bash(then echo \"✅ VITE_GEMINI_API_KEY is set ($#ITE_GEMINI_API_KEY characters)\")", "Bash(cp:*)", "<PERSON><PERSON>(sed:*)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(mv:*)", "WebFetch(domain:openrouter.ai)", "Bash(git reset:*)", "Bash(open test-puter.html)", "Bash(psql:*)", "Bash(open http://localhost:3002)", "<PERSON><PERSON>(open:*)", "mcp__supabase__get_logs", "<PERSON><PERSON>(touch:*)", "mcp__supabase__get_advisors", "Bash(/dev/null)", "Bash(npm install:*)", "<PERSON>sh(timeout 3 npm run dev)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(timeout 10 npm run dev)", "mcp__supabase__get_project", "<PERSON><PERSON>(python3:*)", "Bash(if [ -f .env.local ])", "<PERSON><PERSON>(then echo \"Found .env.local\")", "Bash(else echo \"No .env.local found\")", "<PERSON><PERSON>(source:*)", "Bash(lsof:*)", "Bash(ps:*)", "Bash(awk:*)", "Bash(rg:*)", "<PERSON><PERSON>(chmod:*)", "Bash(git --version)", "Bash(npx eslint:*)", "<PERSON><PERSON>(echo:*)", "Bash(for file in *.ts)", "Bash(done)", "Bash(npx tsx:*)", "Bash(timeout 10s npm run dev:*)", "Bash(if [ -n \"$VITE_MISTRAL_API_KEY\" ])", "Bash(then echo \"✅ VITE_MISTRAL_API_KEY is configured\")", "Bash(elif [ -n \"$VITE_OPENAI_API_KEY\" ])", "Bash(then echo \"✅ VITE_OPENAI_API_KEY is configured\")", "Bash(elif [ -n \"$VITE_GEMINI_API_KEY\" ])", "Bash(then echo \"✅ VITE_GEMINI_API_KEY is configured\")", "Bash(else echo \"ℹ️  No AI API keys detected - system will work in deterministic mode\")", "Bash(VITE_MISTRAL_API_KEY=nsAh2bAAvZMgOxUSIffkddyykrJmbWQ5 npm run dev)", "mcp__supabase__list_edge_functions", "Bash(npm search:*)", "<PERSON><PERSON>(npx playwright test:*)", "Bash(PORT=3000 npm run dev)", "mcp__supabase__deploy_edge_function", "Bash(ls:*)", "mcp__supabase__get_edge_function", "mcp__supabase__get_project_url", "mcp__supabase__list_extensions", "mcp__supabase__search_docs", "Bash(npm audit:*)", "Bash(npm uninstall:*)", "Bash(git rm:*)", "Bash(timeout 60s npm run build)", "Bash(for:*)", "Bash(do basename \"$file\")", "Bash(npx madge:*)", "WebFetch(domain:localhost)", "Bash(do)", "Bash(if [ -f \"$file\" ])", "<PERSON><PERSON>(then)", "<PERSON><PERSON>(else)", "Bash(npm ls:*)", "Bash(./run-security-audit.sh:*)", "Bash(gh auth:*)", "Bash(gh repo create:*)", "Bash(git remote remove:*)", "Bash(git remote add:*)", "Bash(git branch:*)", "<PERSON><PERSON>(git rev-list:*)", "Bash(git filter-branch:*)", "Bash(git init:*)", "Bash(npx vercel:*)", "<PERSON><PERSON>(cat:*)", "WebFetch(domain:audit-ready-sw.vercel.app)", "Bash(gh repo view:*)", "<PERSON><PERSON>(gh run list:*)", "Bash(gh run view:*)", "<PERSON><PERSON>(timeout:*)", "Bash(npm update:*)", "Bash(gh api:*)", "Bash(npm view:*)", "Bash(npx license-checker:*)", "Bash(git pull:*)", "Bash(gh workflow:*)", "Bash(CI=true npm run build)", "Bash(npm test:*)", "Bash(NODE_ENV=development npm test -- src/tests/rag-integration.test.ts --reporter=verbose --run)", "Bash(npm outdated)", "<PERSON><PERSON>(git clean:*)", "Bash(git merge:*)", "Bash(git revert:*)", "Bash(git rebase:*)", "Bash(if grep -q \"createServiceLogger\\|createComponentLogger\" \"$file\")", "Bash(npm ci:*)", "Bash(kill:*)", "Bash(gh repo list:*)", "WebFetch(domain:auditready-sw.vercel.app)", "<PERSON><PERSON>(claude doctor)"], "deny": [], "additionalDirectories": ["/Users/<USER>"]}}
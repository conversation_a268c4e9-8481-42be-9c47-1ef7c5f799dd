name: Dependency Updates

on:
  schedule:
    # Run every Monday at 9 AM UTC
    - cron: '0 9 * * 1'
  workflow_dispatch: # Allow manual triggering

jobs:
  dependency-updates:
    name: Update Dependencies
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: '8'

      - name: Update dependencies
        run: |
          pnpm update --latest --interactive false
          pnpm audit fix

      - name: Check for changes
        id: changes
        run: |
          if git diff --quiet; then
            echo "changed=false" >> $GITHUB_OUTPUT
          else
            echo "changed=true" >> $GITHUB_OUTPUT
          fi

      - name: Create Pull Request
        if: steps.changes.outputs.changed == 'true'
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: 'chore: update dependencies'
          title: 'chore: Weekly dependency updates'
          body: |
            Automated dependency updates created by GitHub Actions.
            
            ### Changes
            - Updated dependencies to latest versions
            - Applied security fixes where available
            
            ### Testing
            Please verify that all tests pass and the application builds successfully.
            
            Auto-generated by [dependency-updates workflow](https://github.com/${{ github.repository }}/actions/workflows/dependency-updates.yml)
          branch: chore/dependency-updates
          delete-branch: true
          labels: |
            dependencies
            automated
import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Zap, 
  Filter, 
  Shield, 
  Lock, 
  Settings, 
  BookOpen, 
  Lightbulb, 
  Factory, 
  ArrowRight 
} from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MarkdownText } from '@/components/ui/MarkdownText';
import { cleanUnifiedRequirementsEngine } from '@/services/compliance/CleanUnifiedRequirementsEngine';
import { FrameworkIdMapper } from '@/services/compliance/FrameworkIdMapper';
import { SectorSpecificEnhancer } from '@/services/compliance/SectorSpecificEnhancer';
import { cleanMarkdownFormatting } from '@/utils/textFormatting';
import { AILoadingAnimation } from '@/components/compliance/AILoadingAnimation';

// CISO-Grade Renderer for clean formatting without markdown
const CISOGradeRenderer: React.FC<{ content: string }> = ({ content }) => {
  if (!content) return null;
  
  // Section titles (Core Requirements, HR, Monitoring & Compliance)
  if (content.match(/^(Core Requirements|HR|Monitoring & Compliance)$/)) {
    return (
      <div className="mt-6 mb-4">
        <h4 className="text-lg font-bold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
          {content}
        </h4>
      </div>
    );
  }
  
  // Requirement titles (a) LEADERSHIP COMMITMENT...)
  const reqTitleMatch = content.match(/^([a-z]\))\s+(.+)$/i);
  if (reqTitleMatch) {
    const [, letter, title] = reqTitleMatch;
    return (
      <div className="mt-4 mb-2">
        <h5 className="font-bold text-gray-900 dark:text-white">
          {letter} {title}
        </h5>
      </div>
    );
  }
  
  // Framework References (blue styling)
  if (content.startsWith('Framework References:')) {
    return (
      <div className="mt-2 mb-2">
        <span className="font-semibold text-blue-600 dark:text-blue-400">
          {content}
        </span>
      </div>
    );
  }
  
  // Framework reference lines (indented blue)
  if (content.match(/^\s+(ISO\/IEC|GDPR|NIS2|DORA|CIS Controls)/)) {
    return (
      <div className="ml-4 text-blue-600 dark:text-blue-400">
        {content.trim()}
      </div>
    );
  }
  
  // Bullet points
  if (content.startsWith('•') || content.startsWith('-')) {
    return (
      <div className="flex items-start space-x-2 my-1">
        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
        <span className="text-gray-700 dark:text-gray-300 leading-relaxed">
          {content.substring(1).trim()}
        </span>
      </div>
    );
  }
  
  // Indented details
  if (content.startsWith('  ')) {
    return (
      <div className="ml-6 text-sm text-gray-600 dark:text-gray-400">
        {content.trim()}
      </div>
    );
  }
  
  // Regular content
  return (
    <div className="text-gray-700 dark:text-gray-300 leading-relaxed my-2">
      {content}
    </div>
  );
};

interface UnifiedRequirementsTabProps {
  organizationId: string;
  selectedFrameworkIds: string[];
  selectedFrameworks: Record<string, any>;
  selectedIndustrySector: string | null;
  unifiedCategoryFilter: string;
  setUnifiedCategoryFilter: (value: string) => void;
  setSelectedGuidanceCategory: (value: string) => void;
  setShowUnifiedGuidance: (value: boolean) => void;
  showGeneration?: boolean;
  isGenerating?: boolean;
  filteredUnifiedMappings: any[];
  restructuringCategories?: any[];
  generateDynamicContentForCategory: (categoryName: string) => Promise<any[]>;
  generatedContent: any;
}

export const UnifiedRequirementsTab: React.FC<UnifiedRequirementsTabProps> = ({
  filteredUnifiedMappings,
  selectedFrameworks,
  selectedIndustrySector,
  unifiedCategoryFilter,
  setUnifiedCategoryFilter,
  restructuringCategories = [],
  generateDynamicContentForCategory,
  setSelectedGuidanceCategory,
  setShowUnifiedGuidance,
  generatedContent,
  showGeneration = false,
  isGenerating = false,
}) => {
  const [cleanGeneratedContent, setCleanGeneratedContent] = React.useState<Map<string, string>>(new Map());
  const [generationStats, setGenerationStats] = React.useState<{
    reductionRatio: number;
    originalLength: number;
    consolidatedLength: number;
  } | null>(null);

  // Generate content for each category using the proper template system
  React.useEffect(() => {
    const generateContentForCategories = async () => {
      if (!filteredUnifiedMappings || filteredUnifiedMappings.length === 0) {
        console.log('⚠️ No unified mappings available for content generation');
        return;
      }

      if (!selectedFrameworks || Object.keys(selectedFrameworks).length === 0) {
        console.log('⚠️ No frameworks selected for content generation');
        return;
      }

      try {
        console.log('🚀 Generating proper template content for', filteredUnifiedMappings.length, 'categories');
        
        const { UnifiedRequirementsBridge } = await import('@/services/compliance/UnifiedRequirementsBridge');
        const contentMap = new Map<string, string>();
        
        // Generate content for each category using the bridge
        for (const mapping of filteredUnifiedMappings) {
          const categoryName = mapping.category.replace(/^\d+\.\s*/, '');
          
          try {
            const categoryContent = await UnifiedRequirementsBridge.generateUnifiedRequirementsForCategory(
              mapping,
              selectedFrameworks
            );
            
            if (categoryContent && categoryContent.length > 0) {
              // Convert array to consolidated string for display
              const consolidatedContent = categoryContent.join('\n\n');
              contentMap.set(categoryName, consolidatedContent);
              console.log(`✅ Generated content for ${categoryName}: ${categoryContent.length} sections`);
            } else {
              console.warn(`⚠️ No content generated for ${categoryName}`);
            }
          } catch (error) {
            console.error(`❌ Error generating content for ${categoryName}:`, error);
          }
        }
        
        setCleanGeneratedContent(contentMap);
        
        // Calculate stats
        const totalOriginal = filteredUnifiedMappings.length * 100; // Estimated
        const totalGenerated = Array.from(contentMap.values()).reduce((sum, content) => sum + content.length, 0);
        const reductionRatio = totalOriginal > 0 ? (totalOriginal - totalGenerated) / totalOriginal : 0;
        
        setGenerationStats({
          reductionRatio: Math.max(0, reductionRatio),
          originalLength: totalOriginal,
          consolidatedLength: totalGenerated
        });
        
        console.log('✅ Template content generation completed for', contentMap.size, 'categories');
      } catch (error) {
        console.error('❌ Error generating template content:', error);
      }
    };

    generateContentForCategories();
  }, [selectedFrameworks, filteredUnifiedMappings]);
  // Helper function to get restructuring status for a category
  const getCategoryStatus = (categoryName: string) => {
    const cleanCategoryName = categoryName.replace(/^\d+\.\s*/, '');
    return restructuringCategories.find((cat: any) => 
      cat.name === categoryName || 
      cat.name === cleanCategoryName ||
      cat.name.replace(/^\d+\.\s*/, '') === cleanCategoryName
    );
  };

  // Convert properly generated content to display format
  const contentObject = React.useMemo(() => {
    const obj: Record<string, any[]> = {};
    
    // Prioritize clean generated content from templates
    cleanGeneratedContent.forEach((content, categoryName) => {
      if (content && content.trim()) {
        // Split by double newlines to get sections, then filter empty sections
        const sections = content.split(/\n\n+/).filter(section => section.trim());
        
        // Store with clean category name for matching
        const cleanName = categoryName.replace(/^\d+\.\s*/, '');
        obj[cleanName] = sections;
        obj[categoryName] = sections; // Also store with original name
        
        console.log(`[CONTENT OBJECT] Template content for "${cleanName}": ${sections.length} sections`);
        if (sections.length > 0) {
          console.log(`[CONTENT OBJECT] First section preview: ${sections[0].substring(0, 100)}...`);
        }
      }
    });
    
    // Fallback to existing generated content if no template content available
    if (Object.keys(obj).length === 0) {
      console.log('[CONTENT OBJECT] No template content available, using fallback generated content');
      
      if (generatedContent && typeof generatedContent.forEach === 'function') {
        generatedContent.forEach((value: any, key: any) => {
          obj[key] = Array.isArray(value) ? value : [];
        });
      } else if (generatedContent && typeof generatedContent === 'object') {
        Object.entries(generatedContent).forEach(([key, value]: [string, any]) => {
          obj[key] = Array.isArray(value) ? value : [];
        });
      }
    }
    
    console.log(`[CONTENT OBJECT] Final content mapping:`, Object.keys(obj).map(key => `${key}: ${obj[key].length} items`));
    return obj;
  }, [cleanGeneratedContent, generatedContent]);
  return (
    <div className="space-y-6">
      <Card className="border border-slate-200 dark:border-slate-700 rounded-xl">
        <CardHeader className="bg-gradient-to-r from-orange-500 to-yellow-500 text-white rounded-t-2xl">
          <CardTitle className="flex items-center space-x-3">
            <div className="p-2 bg-white/20 rounded-lg">
              <Zap className="w-6 h-6" />
            </div>
            <div>
              <h2 className="text-xl font-semibold">AuditReady Unified Requirements</h2>
              <p className="text-sm text-white/80 font-normal">Simplified, comprehensive compliance requirements</p>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-8">
          {/* Framework Unification Introduction */}
          <div className="mb-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
            <div className="flex items-start justify-between mb-3">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                <Filter className="w-5 h-5 mr-2 text-blue-600" />
                Framework Integration Overview
              </h3>
              <div className="text-right">
                <div className="text-xs text-gray-500 dark:text-gray-400">Generated Requirements</div>
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {filteredUnifiedMappings?.length || 0}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">unified groups</div>
              </div>
            </div>
            <p className="text-sm text-gray-700 dark:text-gray-300 mb-3">
              Clean unified requirements generated from your selected compliance frameworks using intelligent text consolidation:
            </p>
            {generationStats && (
              <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
                <div className="flex items-center space-x-2">
                  <Zap className="w-4 h-4 text-green-600" />
                  <span className="text-sm font-medium text-green-800 dark:text-green-200">
                    AI Text Consolidation Active
                  </span>
                </div>
                <p className="text-xs text-green-700 dark:text-green-300 mt-1">
                  {Math.round(generationStats.reductionRatio * 100)}% text reduction achieved through intelligent consolidation
                </p>
              </div>
            )}
            <div className="flex flex-wrap gap-2">
              {selectedFrameworks['iso27001'] && (
                <Badge className="bg-blue-500 text-white px-3 py-1">
                  <Shield className="w-3 h-3 mr-1" />
                  ISO 27001
                </Badge>
              )}
              {selectedFrameworks['iso27002'] && (
                <Badge className="bg-green-500 text-white px-3 py-1">
                  <Lock className="w-3 h-3 mr-1" />
                  ISO 27002
                </Badge>
              )}
              {selectedFrameworks['cisControls'] && (
                <Badge className="bg-purple-500 text-white px-3 py-1">
                  <Settings className="w-3 h-3 mr-1" />
                  CIS Controls {selectedFrameworks['cisControls'].toUpperCase()}
                </Badge>
              )}
              {selectedFrameworks['gdpr'] && (
                <Badge className="bg-orange-500 text-white px-3 py-1">
                  <BookOpen className="w-3 h-3 mr-1" />
                  GDPR
                </Badge>
              )}
              {selectedFrameworks['nis2'] && (
                <Badge className="bg-indigo-500 text-white px-3 py-1">
                  <Shield className="w-3 h-3 mr-1" />
                  NIS2
                </Badge>
              )}
              {selectedFrameworks['dora'] && (
                <Badge className="bg-red-500 text-white px-3 py-1">
                  <Shield className="w-3 h-3 mr-1" />
                  DORA
                </Badge>
              )}
            </div>
            <p className="text-xs text-gray-600 dark:text-gray-400 mt-3">
              Each unified requirement below eliminates duplicate controls and combines overlapping requirements into streamlined, actionable guidelines.
            </p>
            <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
                <div className="text-2xl font-bold text-blue-600">{filteredUnifiedMappings?.length || 0}</div>
                <div className="text-gray-600 dark:text-gray-400">Total Groups</div>
              </div>
              <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3">
                <div className="text-2xl font-bold text-purple-600">
                  {(filteredUnifiedMappings || []).reduce((total: any, group: any) => {
                    return total + (group.auditReadyUnified?.subRequirements?.length || 0);
                  }, 0)}
                </div>
                <div className="text-gray-600 dark:text-gray-400">Total Sub-requirements</div>
              </div>
            </div>
          </div>
          
          {/* Category Filter Dropdown - GitHub Version */}
          <div className="mb-6">
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium">Filter Categories:</span>
            </div>
            <Select 
              value={unifiedCategoryFilter}
              onValueChange={(value) => {
                setUnifiedCategoryFilter(value);
              }}
            >
              <SelectTrigger className="w-full max-w-lg mt-2">
                <SelectValue placeholder="Filter requirement categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {(filteredUnifiedMappings || []).map((mapping: any) => (
                  <SelectItem key={mapping.id} value={mapping.id}>
                    {mapping.category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-6">
            {(() => {
              // Use clean generated categories if no unified mappings exist
              if (!filteredUnifiedMappings || filteredUnifiedMappings.length === 0) {
                console.log('🔄 No unified mappings found, using clean generated categories');
                return Array.from(cleanGeneratedContent.entries()).map(([categoryName, content], index) => {
                  // Create a mapping-like object for clean generated content
                  const fakeMapping = {
                    id: categoryName.toLowerCase().replace(/\s+/g, '-'),
                    category: categoryName,
                    auditReadyUnified: {
                      description: `Consolidated requirements for ${categoryName}`,
                      subRequirements: content.split('\n\n').filter(section => section.trim())
                    },
                    frameworks: {}
                  };
                  return { mapping: fakeMapping, isCleanGenerated: true, index };
                });
              }
              
              // Use existing unified mappings
              return filteredUnifiedMappings.filter((mapping: any) => 
                unifiedCategoryFilter === 'all' || mapping.id === unifiedCategoryFilter
              ).map((mapping: any, index: any) => ({ mapping, isCleanGenerated: false, index }));
            })().map(({ mapping, isCleanGenerated, index }: { mapping: any; isCleanGenerated: any; index: any }) => (
              <motion.div
                key={mapping.id}
                id={`unified-${mapping.id}`}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="border border-slate-200 dark:border-slate-700 rounded-xl p-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/10 dark:to-purple-900/10"
              >
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {mapping.category.replace(/^\d+\. /, '')}
                      </h3>
                      {(() => {
                        const categoryStatus = getCategoryStatus(mapping.category);
                        if (categoryStatus) {
                          if (categoryStatus.status === 'restructuring') {
                            return (
                              <div className="flex items-center space-x-1">
                                <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                                <span className="text-xs text-blue-600 font-medium">AI Restructuring...</span>
                              </div>
                            );
                          } else if (categoryStatus.status === 'completed') {
                            return (
                              <div className="flex items-center space-x-1">
                                <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                  <span className="text-white text-xs">✓</span>
                                </div>
                                <span className="text-xs text-green-600 font-medium">AI Enhanced</span>
                              </div>
                            );
                          } else if (categoryStatus.status === 'error') {
                            return (
                              <div className="flex items-center space-x-1">
                                <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                                  <span className="text-white text-xs">!</span>
                                </div>
                                <span className="text-xs text-red-600 font-medium">Processing Failed</span>
                              </div>
                            );
                          }
                        }
                        return null;
                      })()}
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      {cleanMarkdownFormatting(mapping.auditReadyUnified.description)}
                    </p>
                    <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                      {mapping.category}
                    </Badge>
                  </div>
                  <div className="text-right flex flex-col items-end space-y-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="mb-2 text-xs px-3 py-1 text-emerald-700 border-emerald-600 hover:bg-emerald-50 dark:text-emerald-400 dark:border-emerald-500 dark:hover:bg-emerald-900/20"
                      onClick={() => {
                        // Clean the category name by removing number prefix (e.g., "01. Governance" -> "Governance")
                        const cleanCategoryName = mapping.category.replace(/^\d+\. /, '');
                        console.log('[DEBUG] Original mapping.category:', mapping.category);
                        console.log('[DEBUG] Clean category name:', cleanCategoryName);
                        setSelectedGuidanceCategory(cleanCategoryName);
                        setShowUnifiedGuidance(true);
                      }}
                    >
                      <Lightbulb className="w-3 h-3 mr-1" />
                      Unified Guidance
                    </Button>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Replaces</div>
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                      {(mapping.frameworks?.['iso27001']?.length || 0) + (mapping.frameworks?.['iso27002']?.length || 0) + (mapping.frameworks?.['cisControls']?.length || 0) + (mapping.frameworks?.['gdpr']?.length || 0) + (mapping.frameworks?.['nis2']?.length || 0) + (mapping.frameworks?.['dora']?.length || 0)}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">requirements</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {(() => {
                        const enhancedSubReqs = SectorSpecificEnhancer.enhanceSubRequirements(
                          mapping.auditReadyUnified.subRequirements || [],
                          mapping.category,
                          selectedIndustrySector,
                          selectedFrameworks['nis2']
                        );
                        return enhancedSubReqs.length;
                      })()} sub-requirements{selectedFrameworks['nis2'] && selectedIndustrySector && SectorSpecificEnhancer.hasSectorEnhancements(selectedIndustrySector) ? ' (enhanced)' : ''}
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900 dark:text-white">Implementation Guidelines:</h4>
                  <div className="space-y-4">
                    {(() => {
                      // Use AuditReady unified guidance from database
                      let enhancedSubReqs: string[] = [];
                      
                      // Use dynamic generated content for implementation guidelines
                      const categoryName = mapping.category.replace(/^\d+\.\s*/, '');
                      const dynamicContent = contentObject[categoryName] || [];
                      enhancedSubReqs = dynamicContent;
                      console.log('[IMPLEMENTATION GUIDELINES] Using dynamic content for:', mapping.category, 'sections:', dynamicContent.length);
                      
                      // Simple bullet point display of auditready guidance
                      if (enhancedSubReqs.length === 0) {
                        return (
                          <div className="text-sm text-gray-500 dark:text-gray-400 italic">
                            No implementation guidelines available for this category.
                          </div>
                        );
                      }
                      
                      return (
                        <div className="space-y-3">
                          {enhancedSubReqs.map((subReq: string, i: number) => (
                            <div key={i} className="text-sm">
                              <CISOGradeRenderer content={subReq} />
                            </div>
                          ))}
                        </div>
                      );
                      
                      // Special handling for Governance & Leadership - THREE sections with PROPER organization
                      // Check multiple ways the category might appear
                      const isGovernance = mapping.category === 'Governance & Leadership' || 
                                         mapping.category?.includes('Governance') ||
                                         mapping.category?.toLowerCase().includes('governance');
                      
                      console.log('[SECTION CHECK] Category:', mapping.category, 'Is Governance:', isGovernance);
                      
                      let groupedSubReqs: Record<string, string[]>;
                      
                      if (isGovernance) {
                        // PROPER organization for Governance & Leadership:
                        // Core Requirements: a-g (including g) DOCUMENTED PROCEDURES)
                        // HR: h-i only
                        // Monitoring & Compliance: j-p (starting with j) COMPLIANCE MONITORING)
                        // Find PERSONNEL SECURITY and COMPETENCE MANAGEMENT for HR section
                        const personnelSecurityReq = enhancedSubReqs.find(req => req.includes('PERSONNEL SECURITY'));
                        const competenceManagementReq = enhancedSubReqs.find(req => req.includes('COMPETENCE MANAGEMENT'));
                        
                        // All other requirements go to Core or Monitoring
                        const otherReqs = enhancedSubReqs.filter(req => 
                          !req.includes('PERSONNEL SECURITY') && !req.includes('COMPETENCE MANAGEMENT')
                        );
                        
                        // Make sure g) DOCUMENTED PROCEDURES MANAGEMENT stays in Core
                        const documentedProceduresReq = enhancedSubReqs.find(req => req.includes('DOCUMENTED PROCEDURES MANAGEMENT'));
                        const coreReqs = otherReqs.filter(req => !req.includes('DOCUMENTED PROCEDURES MANAGEMENT')).slice(0, 6);
                        if (documentedProceduresReq) {
                          coreReqs.push(documentedProceduresReq);
                        }
                        
                        groupedSubReqs = {
                          'Core Requirements': coreReqs, // Include g) DOCUMENTED PROCEDURES
                          'HR': [personnelSecurityReq, competenceManagementReq].filter((req): req is string => Boolean(req)),
                          'Monitoring & Compliance': otherReqs.filter(req => !req.includes('DOCUMENTED PROCEDURES MANAGEMENT')).slice(6)
                        };
                        
                        console.log('[✅ GOVERNANCE SECTIONS] Created proper sections:');
                        console.log('Core Requirements (a-g):', groupedSubReqs['Core Requirements']?.length);
                        console.log('HR (h-i):', groupedSubReqs['HR']?.length);
                        console.log('Monitoring & Compliance (j-p):', groupedSubReqs['Monitoring & Compliance']?.length);
                      } else {
                        // Default grouping for other categories
                        groupedSubReqs = {
                          'Core Requirements': enhancedSubReqs.filter((_, i) => i < Math.ceil(enhancedSubReqs.length / 3)),
                          'Implementation Standards': enhancedSubReqs.filter((_, i) => i >= Math.ceil(enhancedSubReqs.length / 3) && i < Math.ceil(enhancedSubReqs.length * 2 / 3)),
                          'Monitoring & Compliance': enhancedSubReqs.filter((_, i) => i >= Math.ceil(enhancedSubReqs.length * 2 / 3))
                        };
                      }
                      
                      return Object.entries(groupedSubReqs).map(([groupName, requirements]) => (
                        requirements.length > 0 && (
                          <div key={groupName} className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
                            <h5 className="font-medium text-sm text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                              <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                              {groupName}
                            </h5>
                            <div className="space-y-2">
                              {(requirements as string[]).map((subReq: string, i: number) => {
                                // Debug logging
                                if (i === 0) {
                                  console.log('[CATEGORY DEBUG] Mapping category:', JSON.stringify(mapping.category));
                                  console.log('[CATEGORY DEBUG] Exact category match?', mapping.category === 'Governance & Leadership');
                                  console.log('[CATEGORY DEBUG] Category includes Governance?', mapping.category?.includes('Governance'));
                                  if (mapping.category?.includes('Governance')) {
                                    console.log('[GOVERNANCE DEBUG] First requirement received:', subReq.substring(0, 200));
                                    console.log('[GOVERNANCE DEBUG] Contains Core Requirements?', subReq.includes('Core Requirements:'));
                                    console.log('[GOVERNANCE DEBUG] Total length:', subReq.length);
                                  }
                                }
                                
                                // Special formatting for Governance & Leadership  
                                const isGovernanceItem = mapping.category === 'Governance & Leadership' || 
                                                        mapping.category?.includes('Governance');
                                
                                if (isGovernanceItem) {
                                  // Split the content by requirement letters and keep both title and description
                                  const parts = subReq.split(/(?=^[a-p]\)\s)/gm).filter(part => part.trim());
                                  
                                  return (
                                    <div key={i} className="space-y-3">
                                      {parts.map((part, partIdx) => {
                                        const trimmed = part.trim();
                                        if (!trimmed) return null;
                                        
                                        // Simple approach: Split on first line break or after reasonable title length
                                        const lines = trimmed.split('\n');
                                        const firstLine = lines[0];
                                        if (!firstLine) return null;
                                        
                                        const restOfLines = lines.slice(1).join('\n').trim();
                                        
                                        // Check if starts with letter pattern
                                        if (firstLine.match(/^[a-p]\)\s+/)) {
                                          // Find where title likely ends - look for common patterns
                                          let titleEnd = firstLine.length;
                                          const indicators = [
                                            ' (ISMS Requirement:',
                                            ' (ISO 27001 Foundation:',
                                            ' (ISO 27001 Requirement:',
                                            ' (ISO 27002 Control',
                                            ') DEVELOPMENT -',
                                            ') INTEGRATION -', 
                                            ' DEVELOPMENT -',
                                            ' INTEGRATION -',
                                            ' ISO ',
                                            ' ISMS ',
                                            ' Define ',
                                            ' Develop ',
                                            ' Establish ',
                                            ' Implement ',
                                            ' Maintain ',
                                            ' Determine ',
                                            ' Core Requirements',
                                            ' FRAMEWORK',
                                            ' AND DEVELOPMENT',
                                            ' - '
                                          ];
                                          
                                          for (const indicator of indicators) {
                                            const index = firstLine.indexOf(indicator);
                                            if (index > 20 && index < titleEnd) { // Must be reasonable distance from start
                                              titleEnd = index;
                                            }
                                          }
                                          
                                          // Special handling for POLICY FRAMEWORK - title should only be "d) POLICY FRAMEWORK"
                                          if (firstLine.includes('POLICY FRAMEWORK')) {
                                            const frameworkEnd = firstLine.indexOf('FRAMEWORK') + 'FRAMEWORK'.length;
                                            titleEnd = frameworkEnd;
                                          }
                                          
                                          let title = firstLine.substring(0, titleEnd).trim();
                                          let description = (firstLine.substring(titleEnd) + '\n' + restOfLines).trim();
                                          
                                          // Clean up title - remove trailing parentheses
                                          title = title.replace(/\s*\)\s*$/, '');
                                          
                                          // Clean up description and format with proper line breaks
                                          description = description
                                            .replace(/^(DEVELOPMENT|INTEGRATION)\s*-?\s*/, '') // Remove leading DEVELOPMENT/INTEGRATION
                                            .replace(/^\s*-\s*/, '') // Remove leading dash
                                            .replace(/\s*\)\s*$/, '') // Remove trailing parentheses
                                            .replace(/\s+/g, ' ') // Normalize whitespace
                                            .trim();
                                          
                                          // Handle Implementation Steps specially
                                          const implementationMatch = description.match(/(.*?)\s*Implementation\s+Steps:\s*(.*)/s);
                                          let mainDescription = description;
                                          let implementationSteps = '';
                                          
                                          if (implementationMatch) {
                                            mainDescription = implementationMatch[1]?.trim() || '';
                                            implementationSteps = implementationMatch[2]?.trim() || '';
                                          }
                                          
                                          // Check for "Core Requirements:" pattern and separate it
                                          const coreRequirementsMatch = mainDescription.match(/(.*?)\s*Core\s+Requirements:\s*(.*)/s);
                                          let preCore = '';
                                          let coreRequirements = '';
                                          let remainingDescription = mainDescription;
                                          
                                          if (coreRequirementsMatch) {
                                            preCore = coreRequirementsMatch[1]?.trim() || '';
                                            coreRequirements = coreRequirementsMatch[2]?.trim() || '';
                                            remainingDescription = preCore;
                                          }
                                          
                                          // Split remaining description into parts for better formatting
                                          const descriptionParts = remainingDescription.split(' - ').filter(part => part.trim());
                                          
                                          return (
                                            <div key={partIdx} className="text-sm text-gray-700 dark:text-gray-300 mb-4">
                                              <div className="font-bold mb-2">{title}</div>
                                              {descriptionParts.length > 0 && (
                                                <div className="ml-4 space-y-2">
                                                  {descriptionParts.map((part, partIndex) => (
                                                    <div key={partIndex} className="leading-relaxed">
                                                      {partIndex === 0 ? part.trim() : `- ${part.trim()}`}
                                                    </div>
                                                  ))}
                                                </div>
                                              )}
                                              {coreRequirements && (
                                                <div className="ml-4 mt-3">
                                                  <div className="font-bold mb-2">Core Requirements:</div>
                                                  <div className="space-y-2">
                                                    {coreRequirements.split(' -').filter(part => part.trim()).map((req, reqIndex) => (
                                                      <div key={reqIndex} className="leading-relaxed">
                                                        {reqIndex === 0 ? req.trim() : `- ${req.trim()}`}
                                                      </div>
                                                    ))}
                                                  </div>
                                                </div>
                                              )}
                                              {implementationSteps && (
                                                <div className="ml-4 mt-3">
                                                  <div className="font-semibold mb-2">Implementation Steps:</div>
                                                  <div className="leading-relaxed">{implementationSteps}</div>
                                                </div>
                                              )}
                                              
                                              {/* FRAMEWORK REFERENCES - SAME AS OTHER CATEGORIES */}
                                              <div className="ml-4 mt-3">
                                                <div className="font-semibold text-blue-400 mb-2">Framework References:</div>
                                                <div className="leading-relaxed text-gray-600 dark:text-gray-400">
{(() => {
                                                    // Get the actual framework references for THIS specific subsection
                                                    const subsectionLetter = title.match(/^([a-p])\)/)?.[1];
                                                    if (!subsectionLetter) {
                                                      return '';
                                                    }
                                                    
                                                    // Get the actual injected requirements from global state
                                                    const globalSubsectionRequirements = (window as any).governanceSubsectionRequirements;
                                                    if (!globalSubsectionRequirements) {
                                                      return '';
                                                    }
                                                    
                                                    // Find requirements for THIS subsection
                                                    const subsectionReqs = globalSubsectionRequirements[subsectionLetter] || [];
                                                    if (subsectionReqs.length === 0) {
                                                      return '';
                                                    }
                                                    
                                                    // Build framework references based on actual injected requirements
                                                    const frameworkRefs = new Map<string, string[]>();
                                                    subsectionReqs.forEach((req: any) => {
                                                      const framework = req.framework;
                                                      const code = req.code || req.id;
                                                      if (framework && code) {
                                                        if (!frameworkRefs.has(framework)) {
                                                          frameworkRefs.set(framework, []);
                                                        }
                                                        frameworkRefs.get(framework)!.push(code);
                                                      }
                                                    });
                                                    
                                                    // Return formatted framework references
                                                    if (frameworkRefs.size > 0) {
                                                      const refTexts: string[] = [];
                                                      frameworkRefs.forEach((codes, framework) => {
                                                        refTexts.push(`${framework}: ${codes.join(', ')}`);
                                                      });
                                                      return refTexts.join(' | ');
                                                    }
                                                    
                                                    return '';
                                                  })()}
                                                </div>
                                              </div>
                                              
                                            </div>
                                          );
                                        }
                                        
                                        // Fallback - use MarkdownText for formatting
                                        console.log('[FALLBACK] Using fallback path for text:', trimmed.substring(0, 100));
                                        return (
                                          <div key={partIdx} className="text-sm text-gray-700 dark:text-gray-300 mb-4">
                                            <MarkdownText text={trimmed} />
                                          </div>
                                        );
                                      })}
                                    </div>
                                  );
                                }
                                
                                // Default display for other categories
                                console.log('🔵 DEFAULT DISPLAY - Item', i, ':', subReq.substring(0, 50));
                                return (
                                  <div key={i} className="flex items-start space-x-2 text-sm">
                                    <ArrowRight className="w-3 h-3 text-blue-500 mt-1 flex-shrink-0" />
                                    <span className="text-gray-700 dark:text-gray-300 leading-relaxed">
                                      <MarkdownText text={subReq} />
                                    </span>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        )
                      ));
                    })()}
                  </div>
                  
                  {/* Industry-Specific Requirements Section */}
                  {selectedIndustrySector && mapping.industrySpecific && mapping.industrySpecific.length > 0 && (
                    <div className="mt-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4 border border-green-200 dark:border-green-700">
                      <h5 className="font-medium text-sm text-green-800 dark:text-green-200 mb-3 flex items-center">
                        <Factory className="w-4 h-4 mr-2" />
                        Industry-Specific Requirements
                        <Badge variant="outline" className="ml-2 text-xs bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300">
                          {selectedIndustrySector}
                        </Badge>
                      </h5>
                      <div className="space-y-3">
                        {mapping.industrySpecific.map((req: any, i: any) => (
                          <div key={i} className="bg-white dark:bg-gray-800 rounded-md p-3 border border-green-200/50 dark:border-green-700/50">
                            <div className="flex items-start justify-between mb-2">
                              <div className="flex items-center gap-2">
                                <span className="text-sm font-medium text-green-700 dark:text-green-300">
                                  {req.code}
                                </span>
                                <Badge variant={
                                  req.relevanceLevel === 'critical' ? 'destructive' :
                                  req.relevanceLevel === 'high' ? 'default' :
                                  req.relevanceLevel === 'standard' ? 'secondary' :
                                  'outline'
                                } className="text-xs">
                                  {req.relevanceLevel}
                                </Badge>
                              </div>
                            </div>
                            <h6 className="font-medium text-sm text-gray-800 dark:text-gray-200 mb-2">
                              {req.title}
                            </h6>
                            <div className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                              {req.description.split('•').filter((item: any) => item.trim()).map((item: any, j: any) => (
                                <div key={j} className="flex items-start space-x-2 mb-1">
                                  <ArrowRight className="w-3 h-3 text-green-500 mt-1 flex-shrink-0" />
                                  <span>{item.trim()}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* AI Generation Overlay */}
      <AnimatePresence>
        {showGeneration && isGenerating && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center"
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-white dark:bg-gray-900 rounded-2xl p-8 shadow-2xl max-w-md w-full mx-4"
            >
              <div className="text-center">
                <div className="relative w-24 h-24 mx-auto mb-6">
                  {/* AI Loading Animation Core */}
                  <motion.div
                    className="absolute inset-0 rounded-full border-4 border-blue-200 dark:border-blue-800"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                  >
                    <div className="absolute top-0 left-1/2 -translate-x-1/2 w-2 h-2 bg-blue-600 rounded-full" />
                    <div className="absolute bottom-0 left-1/2 -translate-x-1/2 w-2 h-2 bg-blue-600 rounded-full" />
                    <div className="absolute left-0 top-1/2 -translate-y-1/2 w-2 h-2 bg-blue-600 rounded-full" />
                    <div className="absolute right-0 top-1/2 -translate-y-1/2 w-2 h-2 bg-blue-600 rounded-full" />
                  </motion.div>
                  <motion.div
                    className="absolute inset-3 rounded-full bg-gradient-to-r from-blue-400 to-purple-400 opacity-20"
                    animate={{ scale: [1, 1.2, 1], opacity: [0.2, 0.4, 0.2] }}
                    transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                  />
                  <motion.div
                    className="absolute inset-6 rounded-full bg-gradient-to-br from-blue-600 to-purple-600 shadow-lg flex items-center justify-center"
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                  >
                    <Zap className="w-6 h-6 text-white" />
                  </motion.div>
                </div>
                
                <motion.h3
                  className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  Generating Unified Requirements
                </motion.h3>
                
                <motion.p
                  className="text-gray-600 dark:text-gray-400 mb-4"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  AI is analyzing your selected frameworks and creating unified compliance requirements...
                </motion.p>

                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden">
                  <motion.div
                    className="h-full bg-gradient-to-r from-blue-600 to-purple-600"
                    initial={{ width: "0%" }}
                    animate={{ width: "100%" }}
                    transition={{ duration: 8, ease: "easeInOut", repeat: Infinity }}
                  />
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
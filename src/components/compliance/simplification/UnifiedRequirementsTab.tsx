import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MarkdownText } from '@/components/ui/MarkdownText';
import { SectorSpecificEnhancer } from '@/services/compliance/SectorSpecificEnhancer';
import {
  Shield,
  Zap,
  Target,
  CheckCircle,
  ArrowRight,
  FileSpreadsheet,
  Download,
  ChevronDown,
  FileText,
  Lightbulb,
  Users,
  BookOpen,
  Lock,
  Settings,
  Eye,
  Filter,
  Factory
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TabsContent } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

interface UnifiedRequirementsTabProps {
  selectedFrameworks: any;
  selectedIndustrySector: string | null;
  industrySectors: any[] | undefined;
  filteredUnifiedMappings: any[];
  unifiedCategoryFilter: string;
  setUnifiedCategoryFilter: (value: string) => void;
  showUnifiedGuidance: boolean;
  setShowUnifiedGuidance: (value: boolean) => void;
  selectedGuidanceCategory: string;
  setSelectedGuidanceCategory: (value: string) => void;
  showFrameworkReferences: boolean;
  setShowFrameworkReferences: (value: boolean) => void;
  showOperationalExcellence: boolean;
  setShowOperationalExcellence: (value: boolean) => void;
  guidanceContent: any;
  generateDynamicContentForCategory: (categoryName: string) => Promise<any[]>;
  isGenerating: boolean;
  setIsGenerating: (value: boolean) => void;
  generatedContent: any;
  setGeneratedContent: (value: any) => void;
  ComplianceExportService: any;
  aiEnvironment: any;
  navigate: any;
}

export const UnifiedRequirementsTab: React.FC<UnifiedRequirementsTabProps> = ({
  selectedFrameworks,
  selectedIndustrySector,
  industrySectors,
  filteredUnifiedMappings,
  unifiedCategoryFilter,
  setUnifiedCategoryFilter,
  showUnifiedGuidance,
  setShowUnifiedGuidance,
  selectedGuidanceCategory,
  setSelectedGuidanceCategory,
  showFrameworkReferences,
  setShowFrameworkReferences,
  showOperationalExcellence,
  setShowOperationalExcellence,
  guidanceContent,
  generateDynamicContentForCategory,
  isGenerating,
  setIsGenerating,
  generatedContent,
  setGeneratedContent,
  ComplianceExportService,
  aiEnvironment,
  navigate
}) => {
  // Get unique categories for the filter
  const availableCategories = [...new Set(filteredUnifiedMappings.map(m => m.category))].sort();

  return (
    <TabsContent value="unified" className="space-y-6">
      <Card className="border-2 border-slate-200 dark:border-slate-700 rounded-2xl">
        <CardHeader className="bg-gradient-to-r from-orange-500 to-yellow-500 text-white rounded-t-2xl">
          <CardTitle className="flex items-center space-x-3">
            <div className="p-2 bg-white/20 rounded-lg">
              <Zap className="w-6 h-6" />
            </div>
            <div>
              <h2 className="text-xl font-semibold">AuditReady Unified Requirements</h2>
              <p className="text-sm text-white/80 font-normal">Simplified, comprehensive compliance requirements</p>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-8">
          {/* Framework Unification Introduction */}
          <div className="mb-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
            <div className="flex items-start justify-between mb-3">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                <Filter className="w-5 h-5 mr-2 text-blue-600" />
                Framework Integration Overview
              </h3>
              <div className="text-right">
                <div className="text-xs text-gray-500 dark:text-gray-400">Generated Requirements</div>
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {filteredUnifiedMappings.length}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">unified groups</div>
              </div>
            </div>
            <p className="text-sm text-gray-700 dark:text-gray-300 mb-3">
              The following unified requirements have been generated by consolidating controls from your selected compliance frameworks{selectedFrameworks['nis2'] && selectedIndustrySector ? ' with sector-specific enhancements for ' + (industrySectors?.find(s => s.id === selectedIndustrySector)?.name || 'selected sector') : ''}:
            </p>
            {selectedFrameworks['nis2'] && selectedIndustrySector && SectorSpecificEnhancer.hasSectorEnhancements(selectedIndustrySector) && (
              <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
                <div className="flex items-center space-x-2">
                  <Factory className="w-4 h-4 text-green-600" />
                  <span className="text-sm font-medium text-green-800 dark:text-green-200">
                    Sector-Specific Enhancements Active
                  </span>
                </div>
                <p className="text-xs text-green-700 dark:text-green-300 mt-1">
                  {SectorSpecificEnhancer.getEnhancementSummary(selectedIndustrySector)} for {industrySectors?.find(s => s.id === selectedIndustrySector)?.name}
                </p>
              </div>
            )}
            <div className="flex flex-wrap gap-2">
              {selectedFrameworks['iso27001'] && (
                <Badge className="bg-blue-500 text-white px-3 py-1">
                  <Shield className="w-3 h-3 mr-1" />
                  ISO 27001
                </Badge>
              )}
              {selectedFrameworks['iso27002'] && (
                <Badge className="bg-green-500 text-white px-3 py-1">
                  <Lock className="w-3 h-3 mr-1" />
                  ISO 27002
                </Badge>
              )}
              {selectedFrameworks['cisControls'] && (
                <Badge className="bg-purple-500 text-white px-3 py-1">
                  <Settings className="w-3 h-3 mr-1" />
                  CIS Controls {selectedFrameworks['cisControls'].toUpperCase()}
                </Badge>
              )}
              {selectedFrameworks['gdpr'] && (
                <Badge className="bg-orange-500 text-white px-3 py-1">
                  <BookOpen className="w-3 h-3 mr-1" />
                  GDPR
                </Badge>
              )}
              {selectedFrameworks['nis2'] && (
                <Badge className="bg-indigo-500 text-white px-3 py-1">
                  <Shield className="w-3 h-3 mr-1" />
                  NIS2
                </Badge>
              )}
              {selectedFrameworks['dora'] && (
                <Badge className="bg-red-500 text-white px-3 py-1">
                  <Shield className="w-3 h-3 mr-1" />
                  DORA
                </Badge>
              )}
            </div>
            <p className="text-xs text-gray-600 dark:text-gray-400 mt-3">
              Each unified requirement below eliminates duplicate controls and combines overlapping requirements into streamlined, actionable guidelines.
            </p>
            <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
                <div className="text-2xl font-bold text-blue-600">{filteredUnifiedMappings.length}</div>
                <div className="text-gray-600 dark:text-gray-400">Total Groups</div>
              </div>
              <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3">
                <div className="text-2xl font-bold text-purple-600">
                  {filteredUnifiedMappings.reduce((total, group) => {
                    const enhancedSubReqs = SectorSpecificEnhancer.enhanceSubRequirements(
                      group.auditReadyUnified.subRequirements || [],
                      group.category,
                      selectedIndustrySector,
                      selectedFrameworks['nis2']
                    );
                    return total + enhancedSubReqs.length;
                  }, 0)}
                </div>
                <div className="text-gray-600 dark:text-gray-400">Sub-Requirements</div>
              </div>
            </div>
          </div>

          {/* Controls Panel */}
          <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl border border-gray-200 dark:border-gray-700">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
              <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                <div className="flex flex-col space-y-1">
                  <label className="text-xs font-medium text-gray-700 dark:text-gray-300">Filter by Category</label>
                  <Select value={unifiedCategoryFilter} onValueChange={setUnifiedCategoryFilter}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="All Categories" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {availableCategories.map(category => (
                        <SelectItem key={category} value={category}>{category}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Button
                    variant={showFrameworkReferences ? "default" : "outline"}
                    size="sm"
                    onClick={() => setShowFrameworkReferences(!showFrameworkReferences)}
                    className="text-xs"
                  >
                    <Eye className="w-3 h-3 mr-1" />
                    {showFrameworkReferences ? "Hide" : "Show"} References
                  </Button>
                  
                  <Button
                    variant={showOperationalExcellence ? "default" : "outline"}
                    size="sm"
                    onClick={() => setShowOperationalExcellence(!showOperationalExcellence)}
                    className="text-xs"
                  >
                    <Target className="w-3 h-3 mr-1" />
                    {showOperationalExcellence ? "Hide" : "Show"} Excellence Tips
                  </Button>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="text-xs">
                      <Download className="w-3 h-3 mr-1" />
                      Export
                      <ChevronDown className="w-3 h-3 ml-1" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => {
                      if (ComplianceExportService && ComplianceExportService.exportToPDF) {
                        ComplianceExportService.exportToPDF(filteredUnifiedMappings, selectedFrameworks, 'unified');
                      }
                    }}>
                      <FileText className="w-4 h-4 mr-2" />
                      Export as PDF
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => {
                      if (ComplianceExportService && ComplianceExportService.exportToExcel) {
                        ComplianceExportService.exportToExcel(filteredUnifiedMappings, selectedFrameworks, 'unified');
                      }
                    }}>
                      <FileSpreadsheet className="w-4 h-4 mr-2" />
                      Export as Excel
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
                
                {/* AI Guidance Toggle */}
                {aiEnvironment && (
                  <Button
                    variant={showUnifiedGuidance ? "default" : "outline"}
                    size="sm"
                    onClick={() => setShowUnifiedGuidance(!showUnifiedGuidance)}
                    className="text-xs"
                  >
                    <Lightbulb className="w-3 h-3 mr-1" />
                    {showUnifiedGuidance ? "Hide" : "Show"} AI Guidance
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* AI Guidance Panel */}
          {showUnifiedGuidance && aiEnvironment && (
            <div className="mb-6 p-6 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-xl border border-purple-200 dark:border-purple-800">
              <div className="flex items-center space-x-3 mb-4">
                <Lightbulb className="w-5 h-5 text-purple-600" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">AI Implementation Guidance</h3>
              </div>
              
              <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4 mb-4">
                <Select value={selectedGuidanceCategory} onValueChange={setSelectedGuidanceCategory}>
                  <SelectTrigger className="w-full sm:w-64">
                    <SelectValue placeholder="Select category for guidance" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {availableCategories.map(category => (
                      <SelectItem key={category} value={category}>{category}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Button
                  onClick={async () => {
                    if (!selectedGuidanceCategory || selectedGuidanceCategory === 'all') return;
                    setIsGenerating(true);
                    try {
                      const content = await generateDynamicContentForCategory(selectedGuidanceCategory);
                      setGeneratedContent(content);
                    } catch (error) {
                      console.error('Error generating content:', error);
                    } finally {
                      setIsGenerating(false);
                    }
                  }}
                  disabled={!selectedGuidanceCategory || selectedGuidanceCategory === 'all' || isGenerating}
                  className="whitespace-nowrap"
                >
                  {isGenerating ? "Generating..." : "Generate Guidance"}
                </Button>
              </div>
              
              {guidanceContent && (
                <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                  <div className="prose dark:prose-invert max-w-none">
                    <MarkdownText content={guidanceContent} />
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Unified Requirements Display */}
          <div className="space-y-6">
            {filteredUnifiedMappings
              .filter(mapping => unifiedCategoryFilter === 'all' || mapping.category === unifiedCategoryFilter)
              .map((mapping, index) => {
                // Get enhanced sub-requirements with sector-specific modifications
                const enhancedSubReqs = SectorSpecificEnhancer.enhanceSubRequirements(
                  mapping.auditReadyUnified.subRequirements || [],
                  mapping.category,
                  selectedIndustrySector,
                  selectedFrameworks['nis2']
                );

                return (
                  <motion.div
                    key={`unified-${mapping.id || index}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card className="border border-gray-200 dark:border-gray-700 rounded-xl shadow-sm hover:shadow-md transition-shadow">
                      <CardHeader className="pb-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <Badge 
                                variant="secondary" 
                                className="text-xs font-medium px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200"
                              >
                                {mapping.category}
                              </Badge>
                              {mapping.auditReadyUnified.priority && (
                                <Badge
                                  variant={mapping.auditReadyUnified.priority === 'high' ? 'destructive' : 
                                           mapping.auditReadyUnified.priority === 'medium' ? 'default' : 'secondary'}
                                  className="text-xs"
                                >
                                  {mapping.auditReadyUnified.priority} priority
                                </Badge>
                              )}
                            </div>
                            <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white leading-tight">
                              {mapping.auditReadyUnified.title}
                            </CardTitle>
                            {mapping.auditReadyUnified.description && (
                              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2 leading-relaxed">
                                {mapping.auditReadyUnified.description}
                              </p>
                            )}
                          </div>
                          <div className="ml-4 text-right flex-shrink-0">
                            <div className="text-xs text-gray-500 dark:text-gray-400">Requirements</div>
                            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                              {enhancedSubReqs.length}
                            </div>
                          </div>
                        </div>
                      </CardHeader>
                      
                      <CardContent className="pt-0">
                        {/* Sub-requirements */}
                        {enhancedSubReqs.length > 0 && (
                          <div className="space-y-3">
                            <div className="flex items-center space-x-2">
                              <CheckCircle className="w-4 h-4 text-green-600" />
                              <h4 className="text-sm font-semibold text-gray-900 dark:text-white">Implementation Requirements</h4>
                            </div>
                            <div className="grid gap-3">
                              {enhancedSubReqs.map((req, reqIndex) => (
                                <div
                                  key={reqIndex}
                                  className="p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700"
                                >
                                  <div className="flex items-start space-x-2">
                                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                                    <div className="flex-1">
                                      <div className="text-sm text-gray-900 dark:text-white font-medium">
                                        {req}
                                      </div>
                                      {req.includes('NIS2') && (
                                        <div className="flex items-center space-x-1 mt-2">
                                          <Factory className="w-3 h-3 text-green-600" />
                                          <span className="text-xs text-green-600 font-medium">Sector-specific enhancement</span>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Framework References */}
                        {showFrameworkReferences && mapping.frameworks && (
                          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
                            <div className="flex items-center space-x-2 mb-3">
                              <FileText className="w-4 h-4 text-blue-600" />
                              <h4 className="text-sm font-semibold text-gray-900 dark:text-white">Framework References</h4>
                            </div>
                            <div className="space-y-3">
                              {Object.entries(mapping.frameworks).map(([framework, requirements]: [string, any]) => {
                                if (!selectedFrameworks[framework] || !requirements || requirements.length === 0) return null;
                                
                                const frameworkName = {
                                  'iso27001': 'ISO/IEC 27001',
                                  'iso27002': 'ISO/IEC 27002', 
                                  'cisControls': 'CIS Controls v8',
                                  'gdpr': 'GDPR',
                                  'nis2': 'NIS2 Directive',
                                  'dora': 'DORA'
                                }[framework] || framework;

                                return (
                                  <div key={framework} className="border-l-2 border-blue-300 pl-3">
                                    <div className="text-xs font-medium text-blue-800 dark:text-blue-200 mb-1">
                                      {frameworkName}
                                    </div>
                                    <div className="space-y-1">
                                      {requirements.slice(0, 3).map((req: any, reqIndex: number) => (
                                        <div key={reqIndex} className="text-xs text-gray-700 dark:text-gray-300">
                                          <span className="font-medium">{req.id || req.control_id}:</span> {req.title || req.text}
                                        </div>
                                      ))}
                                      {requirements.length > 3 && (
                                        <div className="text-xs text-gray-500 dark:text-gray-400 italic">
                                          ... and {requirements.length - 3} more controls
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        )}

                        {/* Operational Excellence Tips */}
                        {showOperationalExcellence && mapping.auditReadyUnified.operationalExcellence && (
                          <div className="mt-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
                            <div className="flex items-center space-x-2 mb-3">
                              <Target className="w-4 h-4 text-green-600" />
                              <h4 className="text-sm font-semibold text-gray-900 dark:text-white">Operational Excellence Tips</h4>
                            </div>
                            <div className="text-sm text-gray-700 dark:text-gray-300">
                              <MarkdownText content={mapping.auditReadyUnified.operationalExcellence} />
                            </div>
                          </div>
                        )}

                        {/* Action Button */}
                        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                          <Button
                            onClick={() => navigate(`/compliance-assessment/${mapping.category?.toLowerCase().replace(/\s+/g, '-')}`)}
                            className="w-full sm:w-auto"
                            variant="outline"
                          >
                            <ArrowRight className="w-4 h-4 mr-2" />
                            Start Implementation Assessment
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                );
              })}
          </div>
        </CardContent>
      </Card>
    </TabsContent>
  );
};
/**
 * Unified Requirements Tab Content
 * Extracted from ComplianceSimplification.tsx to reduce complexity
 * 
 * FEATURES:
 * - Framework integration overview
 * - Unified requirements display
 * - Export functionality
 * - Sector-specific enhancements
 * - Requirements categorization
 */

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { 
  Zap, 
  Filter,
  Shield,
  Lock,
  Settings,
  BookOpen,
  Factory,
  Download,
  FileText,
  ChevronDown,
  Lightbulb,
  Eye
} from 'lucide-react';
import { MarkdownText } from '@/components/ui/MarkdownText';
import { SectorSpecificEnhancer } from '@/services/compliance/SectorSpecificEnhancer';
import { ProfessionalPDFExportService } from '@/services/compliance/export/ProfessionalPDFExportService';
import { cleanComplianceSubRequirement } from '@/utils/textFormatting';
import type { SelectedFrameworks } from '@/utils/FrameworkUtilities';

export interface UnifiedRequirementsTabContentProps {
  selectedFrameworks: SelectedFrameworks;
  selectedIndustrySector: string | null;
  filteredUnifiedMappings: any[];
  guidanceContent: Record<string, string>;
  loadGuidanceContent: (categoryName: string) => Promise<void>;
  industrySectors?: Array<{
    id: string;
    name: string;
    description: string;
    nis2Essential: boolean;
    nis2Important: boolean;
  }>;
}

export function UnifiedRequirementsTabContent({
  selectedFrameworks,
  selectedIndustrySector,
  filteredUnifiedMappings,
  guidanceContent,
  loadGuidanceContent,
  industrySectors
}: UnifiedRequirementsTabContentProps) {
  
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  
  const toggleCategory = (categoryName: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryName)) {
      newExpanded.delete(categoryName);
    } else {
      newExpanded.add(categoryName);
    }
    setExpandedCategories(newExpanded);
  };

  const exportToPDF = async () => {
    const pdfService = new ProfessionalPDFExportService();
    await pdfService.exportToPDF({
      type: 'unified',
      title: 'Unified Compliance Requirements',
      data: filteredUnifiedMappings,
      selectedFrameworks,
      industrySector: selectedIndustrySector ? industrySectors?.find(s => s.id === selectedIndustrySector) : undefined
    });
  };

  return (
    <Card className="border-2 border-slate-200 dark:border-slate-700 rounded-2xl">
      <CardHeader className="bg-gradient-to-r from-orange-500 to-yellow-500 text-white rounded-t-2xl">
        <CardTitle className="flex items-center space-x-3">
          <div className="p-2 bg-white/20 rounded-lg">
            <Zap className="w-6 h-6" />
          </div>
          <div>
            <h2 className="text-xl font-semibold">AuditReady Unified Requirements</h2>
            <p className="text-sm text-white/80 font-normal">Simplified, comprehensive compliance requirements</p>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-8">
        {/* Framework Unification Introduction */}
        <div className="mb-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
          <div className="flex items-start justify-between mb-3">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <Filter className="w-5 h-5 mr-2 text-blue-600" />
              Framework Integration Overview
            </h3>
            <div className="text-right">
              <div className="text-xs text-gray-500 dark:text-gray-400">Generated Requirements</div>
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {filteredUnifiedMappings.length}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">unified groups</div>
            </div>
          </div>
          <p className="text-sm text-gray-700 dark:text-gray-300 mb-3">
            The following unified requirements have been generated by consolidating controls from your selected compliance frameworks{selectedFrameworks['nis2'] && selectedIndustrySector ? ' with sector-specific enhancements for ' + (industrySectors?.find(s => s.id === selectedIndustrySector)?.name || 'selected sector') : ''}:
          </p>
          {selectedFrameworks['nis2'] && selectedIndustrySector && SectorSpecificEnhancer.hasSectorEnhancements(selectedIndustrySector) && (
            <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
              <div className="flex items-center space-x-2">
                <Factory className="w-4 h-4 text-green-600" />
                <span className="text-sm font-medium text-green-800 dark:text-green-200">
                  Sector-Specific Enhancements Active
                </span>
              </div>
              <p className="text-xs text-green-700 dark:text-green-300 mt-1">
                {SectorSpecificEnhancer.getEnhancementSummary(selectedIndustrySector)} for {industrySectors?.find(s => s.id === selectedIndustrySector)?.name}
              </p>
            </div>
          )}
          <div className="flex flex-wrap gap-2">
            {selectedFrameworks['iso27001'] && (
              <Badge className="bg-blue-500 text-white px-3 py-1">
                <Shield className="w-3 h-3 mr-1" />
                ISO 27001
              </Badge>
            )}
            {selectedFrameworks['iso27002'] && (
              <Badge className="bg-green-500 text-white px-3 py-1">
                <Lock className="w-3 h-3 mr-1" />
                ISO 27002
              </Badge>
            )}
            {selectedFrameworks['cisControls'] && (
              <Badge className="bg-purple-500 text-white px-3 py-1">
                <Settings className="w-3 h-3 mr-1" />
                CIS Controls {selectedFrameworks['cisControls'].toString().toUpperCase()}
              </Badge>
            )}
            {selectedFrameworks['gdpr'] && (
              <Badge className="bg-orange-500 text-white px-3 py-1">
                <BookOpen className="w-3 h-3 mr-1" />
                GDPR
              </Badge>
            )}
            {selectedFrameworks['nis2'] && (
              <Badge className="bg-indigo-500 text-white px-3 py-1">
                <Shield className="w-3 h-3 mr-1" />
                NIS2
              </Badge>
            )}
            {selectedFrameworks['dora'] && (
              <Badge className="bg-red-500 text-white px-3 py-1">
                <Shield className="w-3 h-3 mr-1" />
                DORA
              </Badge>
            )}
          </div>
          <p className="text-xs text-gray-600 dark:text-gray-400 mt-3">
            Each unified requirement below eliminates duplicate controls and combines overlapping requirements into streamlined, actionable guidelines.
          </p>
          <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
              <div className="text-2xl font-bold text-blue-600">{filteredUnifiedMappings.length}</div>
              <div className="text-gray-600 dark:text-gray-400">Total Groups</div>
            </div>
            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3">
              <div className="text-2xl font-bold text-purple-600">
                {filteredUnifiedMappings.reduce((total, group) => {
                  const enhancedSubReqs = SectorSpecificEnhancer.enhanceSubRequirements(
                    group.auditReadyUnified.subRequirements || [],
                    group.category,
                    selectedIndustrySector,
                    selectedFrameworks['nis2']
                  );
                  return total + enhancedSubReqs.length;
                }, 0)}
              </div>
              <div className="text-gray-600 dark:text-gray-400">Sub-Requirements</div>
            </div>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex flex-wrap gap-4 mb-8">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                <Download className="w-4 h-4 mr-2" />
                Export
                <ChevronDown className="w-4 h-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={exportToPDF}>
                <FileText className="w-4 h-4 mr-2" />
                Export to PDF
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Enhanced unified requirements display with proper deduplication logic and better visuals */}
        <div className="space-y-6">
          {filteredUnifiedMappings.map((group, index) => {
            const isExpanded = expandedCategories.has(group.category);
            const enhancedSubRequirements = SectorSpecificEnhancer.enhanceSubRequirements(
              group.auditReadyUnified.subRequirements || [],
              group.category,
              selectedIndustrySector,
              selectedFrameworks['nis2']
            );

            return (
              <motion.div
                key={group.id || group.category}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="border border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden"
              >
                <div 
                  className="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-blue-900/20 p-6 cursor-pointer hover:from-gray-100 hover:to-blue-100 dark:hover:from-gray-700 dark:hover:to-blue-800/30 transition-all duration-200"
                  onClick={() => toggleCategory(group.category)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-blue-500 rounded-lg">
                        <Lightbulb className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                          {group.category}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {enhancedSubRequirements.length} unified requirements
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={(e) => {
                          e.stopPropagation();
                          loadGuidanceContent(group.category);
                        }}
                        className="text-xs"
                      >
                        <Eye className="w-3 h-3 mr-1" />
                        Show Guidance
                      </Button>
                      <motion.div
                        animate={{ rotate: isExpanded ? 180 : 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        <ChevronDown className="w-5 h-5 text-gray-500" />
                      </motion.div>
                    </div>
                  </div>
                </div>

                {isExpanded && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden"
                  >
                    <div className="p-6 bg-white dark:bg-gray-900">
                      {/* Category Description */}
                      <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <p className="text-sm text-gray-700 dark:text-gray-300">
                          {group.auditReadyUnified.description}
                        </p>
                      </div>

                      {/* Unified Requirements */}
                      <div className="space-y-4">
                        <h4 className="font-semibold text-gray-900 dark:text-white flex items-center">
                          <Zap className="w-4 h-4 mr-2 text-orange-500" />
                          Unified Requirements:
                        </h4>
                        <div className="space-y-3">
                          {enhancedSubRequirements.map((req, reqIndex) => (
                            <div 
                              key={reqIndex}
                              className="flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                            >
                              <div className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-semibold">
                                {String.fromCharCode(97 + reqIndex)}
                              </div>
                              <div className="flex-1">
                                <MarkdownText className="text-sm text-gray-700 dark:text-gray-300">
                                  {cleanComplianceSubRequirement(req)}
                                </MarkdownText>
                                
                                {/* Framework References - Blue Header */}
                                <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                                  <div className="font-semibold text-blue-600 dark:text-blue-400 mb-2">
                                    Framework References:
                                  </div>
                                  <div className="text-xs text-gray-600 dark:text-gray-400">
                                    Related framework controls and standards for this requirement
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Show guidance if available */}
                      {guidanceContent[group.category] && (
                        <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                          <h4 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                            <Lightbulb className="w-4 h-4 mr-2 text-yellow-600" />
                            Implementation Guidance:
                          </h4>
                          <MarkdownText className="text-sm text-gray-700 dark:text-gray-300 prose prose-sm max-w-none">
                            {guidanceContent[group.category]}
                          </MarkdownText>
                        </div>
                      )}
                    </div>
                  </motion.div>
                )}
              </motion.div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}

export default UnifiedRequirementsTabContent;
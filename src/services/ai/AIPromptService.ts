/**
 * 🤖 AI Prompt Service
 * Handles AI prompt generation and Gemini API integration for diagram generation
 * Extracted from OneShotDiagramService for better maintainability and separation of concerns
 */

import type { OneShotDiagramRequest, OneShotDiagramResponse } from './OneShotDiagramService';

interface ProcessContext {
  domain: string;
  startLabel: string;
  defaultTitle: string;
  simpleNodeCount: string;
  mediumNodeCount: string;
  complexNodeCount: string;
  specificInstructions: string;
  requirements: string;
  shapeGuidance: string;
}

export class AIPromptService {
  private static instance: AIPromptService;
  private readonly GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent';

  private constructor() {}

  public static getInstance(): AIPromptService {
    if (!AIPromptService.instance) {
      AIPromptService.instance = new AIPromptService();
    }
    return AIPromptService.instance;
  }

  /**
   * 🤖 GEMINI API INTEGRATION
   * Generates diagram using Gemini AI with sophisticated prompts
   */
  public async generateWithGemini(request: OneShotDiagramRequest, apiKey: string): Promise<OneShotDiagramResponse> {
    const systemPrompt = this.buildProcessSpecificSystemPrompt(request);
    const userPrompt = this.buildProcessSpecificUserPrompt(request);

    const response = await fetch(`${this.GEMINI_API_URL}?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{ text: `${systemPrompt}\n\nUser Request: ${userPrompt}` }]
        }],
        generationConfig: {
          temperature: 0.7,
          maxOutputTokens: 3000,
          topP: 0.8,
          topK: 40
        },
        safetySettings: [
          { category: 'HARM_CATEGORY_HARASSMENT', threshold: 'BLOCK_MEDIUM_AND_ABOVE' },
          { category: 'HARM_CATEGORY_HATE_SPEECH', threshold: 'BLOCK_MEDIUM_AND_ABOVE' },
          { category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT', threshold: 'BLOCK_MEDIUM_AND_ABOVE' },
          { category: 'HARM_CATEGORY_DANGEROUS_CONTENT', threshold: 'BLOCK_MEDIUM_AND_ABOVE' }
        ]
      })
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: { message: response.statusText } }));
      throw new Error(`Gemini API error: ${error.error?.message || response.statusText}`);
    }

    const data = await response.json();
    const generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text;
    
    if (!generatedText) {
      throw new Error('No content generated by Gemini API');
    }

    // Parse the response using validation service
    const { DiagramValidationService } = require('./DiagramValidationService');
    return DiagramValidationService.getInstance().parseGeminiResponse(generatedText, request);
  }

  /**
   * 🎯 SYSTEM PROMPT BUILDER
   * Creates comprehensive system prompts based on diagram type and domain context
   */
  public buildProcessSpecificSystemPrompt(request: OneShotDiagramRequest): string {
    const processContext = this.getProcessContext(request.prompt, request.diagramType);
    
    return `You are an expert diagram designer specializing in ${processContext.domain} processes. Create a professional ${request.diagramType} diagram using ReactFlow node/edge format.

🚫 ABSOLUTELY FORBIDDEN:
- Do NOT create simple linear "Step 1, Step 2, Step 3..." sequences
- Do NOT use generic labels like "Process Step 1", "Task 2", etc.  
- Do NOT create workflows without decision points
- Do NOT make straight-line processes without branches or loops

✅ MANDATORY REQUIREMENTS:
${processContext.specificInstructions}

TECHNICAL REQUIREMENTS:
- Return ONLY valid JSON with "nodes" and "edges" arrays
- Each node must have: id, type: "custom", position: {x, y}, data: {label, shape, fillColor, strokeColor, textColor}
- Each edge must have: id, source, target, type, style, markerEnd
- Use professional ${request.diagramType} conventions for ${processContext.domain}
- Include meaningful process-specific labels and logical connections
- Apply industry-standard styling and colors
- MUST include multiple decision points (diamond shapes)
- MUST show parallel processes and feedback loops
- MUST use specific business terminology, not generic steps

${processContext.shapeGuidance}

Color Coding Guidelines:
- Start/End nodes: Blue/Green circles (#dbeafe/#dcfce7)
- Process steps: Light gray rectangles (#f1f5f9)
- Decision points: Yellow diamonds (#fef3c7)
- Critical/Risk nodes: Red rectangles (#fee2e2)
- Success/Completion: Green rectangles (#dcfce7)

Example Response Structure:
{
  "nodes": [
    {
      "id": "start",
      "type": "custom", 
      "position": {"x": 400, "y": 50},
      "data": {
        "label": "${processContext.startLabel}",
        "shape": "circle",
        "fillColor": "#dbeafe", 
        "strokeColor": "#2563eb",
        "textColor": "#1e293b"
      }
    }
  ],
  "edges": [
    {
      "id": "edge1",
      "source": "start",
      "target": "process1", 
      "type": "smoothstep",
      "style": {"stroke": "#1e293b", "strokeWidth": 2},
      "markerEnd": {"type": "arrowclosed", "color": "#1e293b"}
    }
  ],
  "title": "${processContext.defaultTitle}",
  "description": "Professional ${processContext.domain.toLowerCase()} process diagram",
  "confidence": 0.9
}`;
  }

  /**
   * 👤 USER PROMPT BUILDER  
   * Creates user-specific prompts with complexity and industry context
   */
  public buildProcessSpecificUserPrompt(request: OneShotDiagramRequest): string {
    const processContext = this.getProcessContext(request.prompt, request.diagramType);
    let prompt = `Create a comprehensive ${request.diagramType} diagram for: ${request.prompt}`;
    
    // Add process-specific requirements
    prompt += `\n\n${processContext.requirements}`;
    
    if (request.diagramType === 'gantt') {
      prompt += `\n\nGantt Chart Requirements:
- Use "gantt-task" shape for tasks and "gantt-milestone" shape for milestones
- Calculate realistic start/end dates and durations based on industry standards
- Include logical task dependencies and critical path
- Position nodes to show timeline progression (x-axis = time, y-axis = tasks)
- Use colors to indicate priority levels and resource allocation`;
    }

    // Add complexity-specific guidance
    if (request.complexity) {
      const complexityMap = {
        simple: `Simple approach with ${processContext.simpleNodeCount} main components focusing on core ${processContext.domain.toLowerCase()} steps`,
        medium: `Moderate complexity with ${processContext.mediumNodeCount} components including decision points and ${processContext.domain.toLowerCase()} best practices`,
        complex: `Comprehensive approach with ${processContext.complexNodeCount}+ components, multiple decision points, parallel processes, feedback loops, and complete ${processContext.domain.toLowerCase()} workflow coverage`
      };
      prompt += `\n\nComplexity Level: ${complexityMap[request.complexity]}`;
    }

    // Add industry context
    if (request.industry && request.industry !== 'general') {
      prompt += `\n\nIndustry Context: Apply ${request.industry} industry standards and best practices. Include industry-specific terminology and compliance considerations.`;
    }

    return prompt;
  }

  /**
   * 🎯 PROCESS CONTEXT ANALYZER
   * Determines the specific context and requirements for different process types
   */
  private getProcessContext(prompt: string, diagramType: string): ProcessContext {
    const promptLower = prompt.toLowerCase();
    
    // Risk Management Process Context
    if (promptLower.includes('risk') && (promptLower.includes('management') || promptLower.includes('assessment') || promptLower.includes('analysis'))) {
      return {
        domain: 'Risk Management',
        startLabel: 'Risk Identification',
        defaultTitle: 'Risk Management Process',
        simpleNodeCount: '5-7',
        mediumNodeCount: '8-12',
        complexNodeCount: '15',
        specificInstructions: `Create a comprehensive risk management flowchart with proper decision points, parallel processes, and feedback loops. NEVER create simple linear "Step 1, Step 2" processes. This must be a professional risk management workflow with:
- Multiple decision diamonds for risk evaluation gates
- Parallel processing paths for different risk categories
- Feedback loops from monitoring back to identification
- Branch points for different treatment options
- Approval gates and escalation paths`,
        requirements: `Risk Management Process Requirements (MANDATORY DECISION POINTS):
1. START: Risk Identification (circle, blue)
2. Risk Register Check (diamond): "Risk already in register?" → Yes/No paths
3. Risk Assessment (rectangle): Analyze likelihood and impact
4. Risk Level Decision (diamond): "Risk level?" → Low/Medium/High/Critical paths
5. Treatment Strategy (diamond): "Treatment approach?" → Accept/Mitigate/Transfer/Avoid
6. For MITIGATE path: Risk Mitigation Planning → Implementation → Monitoring
7. For TRANSFER path: Insurance/Outsourcing evaluation → Contract setup
8. For AVOID path: Process redesign → Alternative approach
9. For ACCEPT path: Direct to monitoring
10. Monitoring & Review (rectangle) → feeds back to Risk Assessment
11. Escalation Decision (diamond): "Requires escalation?" → Management/Board approval
12. Risk Register Update (parallelogram) 
13. END: Risk Accepted/Mitigated (circle, green)

CRITICAL: Include at least 4-5 decision diamonds, show parallel paths, and feedback loops.`,
        shapeGuidance: `Shape Usage (ENFORCED):
- Circles: Start/End points only (Risk Identification, Final Acceptance)
- Rectangles: Process activities (Analysis, Planning, Implementation, Monitoring)
- Diamonds: ALL decision points (Register check, Risk level, Treatment choice, Escalation needs)
- Parallelograms: Documentation outputs (Risk Register, Reports, Contracts)`
      };
    }
    
    // Audit Process Context
    if (promptLower.includes('audit') || promptLower.includes('compliance') || promptLower.includes('assessment')) {
      return {
        domain: 'Audit & Compliance',
        startLabel: 'Audit Planning',
        defaultTitle: 'Audit Process Flow',
        simpleNodeCount: '6-8',
        mediumNodeCount: '10-15',
        complexNodeCount: '20',
        specificInstructions: `Create a comprehensive audit process with proper phases: Planning, Fieldwork, Reporting, and Follow-up. Include compliance checkpoints and evidence collection.`,
        requirements: `Audit Process Requirements:
- Start with Audit Planning and Scope Definition
- Include Risk Assessment and Control Testing phases
- Show Evidence Collection and Documentation steps
- Add Findings Analysis and Gap Identification
- Include Draft Report and Management Review processes
- Show Final Report generation and Follow-up actions
- Add compliance validation checkpoints
- Include stakeholder communication touchpoints`,
        shapeGuidance: `Shape Usage:
- Circles: Process start/end points
- Rectangles: Audit activities and documentation
- Diamonds: Compliance checkpoints and approval gates
- Hexagons: External stakeholder interactions`
      };
    }
    
    // Software Development Context
    if (promptLower.includes('software') || promptLower.includes('development') || promptLower.includes('app') || promptLower.includes('system')) {
      return {
        domain: 'Software Development',
        startLabel: 'Requirements Gathering',
        defaultTitle: 'Software Development Process',
        simpleNodeCount: '5-7',
        mediumNodeCount: '8-12',
        complexNodeCount: '15',
        specificInstructions: `Focus on SDLC phases with proper gates, testing, and deployment processes. Include version control, code review, and quality assurance.`,
        requirements: `Software Development Requirements:
- Include all SDLC phases: Planning, Analysis, Design, Implementation, Testing, Deployment
- Show code review and quality assurance gates
- Include testing phases: Unit, Integration, System, User Acceptance
- Add deployment pipeline and rollback procedures
- Show iterative feedback loops and agile practices
- Include version control and branch management
- Add monitoring and maintenance phases`,
        shapeGuidance: `Shape Usage:
- Circles: Project milestones and releases
- Rectangles: Development activities
- Diamonds: Quality gates and decision points
- Parallelograms: Documentation and deliverables`
      };
    }
    
    // Incident Response Context
    if (promptLower.includes('incident') && promptLower.includes('response')) {
      return {
        domain: 'Incident Response',
        startLabel: 'Incident Detection',
        defaultTitle: 'Incident Response Process',
        simpleNodeCount: '6-8',
        mediumNodeCount: '10-14',
        complexNodeCount: '18',
        specificInstructions: `Create a comprehensive incident response workflow following NIST guidelines with Detection, Analysis, Containment, Eradication, Recovery phases.`,
        requirements: `Incident Response Requirements:
- Start with Incident Detection and Reporting
- Include Initial Triage and Classification (use severity diamonds)
- Show Analysis and Investigation phases
- Include Containment strategies (Short-term and Long-term)
- Add Eradication and Recovery processes
- Show Post-Incident Review and Lessons Learned
- Include communication and escalation paths
- Add evidence preservation and forensics steps`,
        shapeGuidance: `Shape Usage:
- Circles: Detection triggers and closure
- Rectangles: Response activities and procedures
- Diamonds: Severity assessment and escalation decisions
- Parallelograms: Documentation and reports`
      };
    }
    
    // Business Process Context
    if (promptLower.includes('business') || promptLower.includes('workflow') || promptLower.includes('process')) {
      return {
        domain: 'Business Process',
        startLabel: 'Process Initiation',
        defaultTitle: 'Business Process Flow',
        simpleNodeCount: '5-7',
        mediumNodeCount: '8-12',
        complexNodeCount: '15',
        specificInstructions: `Focus on business process optimization with clear roles, responsibilities, and handoff points. Include approval workflows and exception handling.`,
        requirements: `Business Process Requirements:
- Define clear start and end points
- Include role-based swim lanes where appropriate
- Show approval workflows and decision gates
- Add exception handling and error paths
- Include timing and SLA considerations
- Show system integrations and touchpoints
- Add performance metrics and KPIs
- Include feedback and improvement loops`,
        shapeGuidance: `Shape Usage:
- Circles: Process triggers and completion
- Rectangles: Business activities and tasks
- Diamonds: Business decisions and approvals
- Parallelograms: Documents and forms`
      };
    }
    
    // Default Generic Context
    return {
      domain: 'General Process',
      startLabel: 'Process Start',
      defaultTitle: 'Process Flow Diagram',
      simpleNodeCount: '4-6',
      mediumNodeCount: '7-10',
      complexNodeCount: '12',
      specificInstructions: `Create a logical process flow with clear sequence and decision points.`,
      requirements: `General Process Requirements:
- Define clear start and end points
- Show logical sequence of activities
- Include decision points where appropriate
- Add parallel processes if relevant
- Include feedback loops and iterations
- Show error handling where needed`,
      shapeGuidance: `Shape Usage:
- Circles: Start/end points
- Rectangles: Process activities
- Diamonds: Decision points
- Parallelograms: Input/output documents`
    };
  }
}
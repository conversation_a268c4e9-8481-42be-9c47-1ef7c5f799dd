# Microsoft Entra ID Configuration
# These environment variables must be set in Supabase Edge Functions

# Entra ID Tenant ID (from Azure portal)
ENTRA_TENANT_ID=your-tenant-id-here

# Entra ID Application (client) ID (from Azure app registration)
ENTRA_CLIENT_ID=your-client-id-here

# Entra ID Client Secret (from Azure app registration)
ENTRA_CLIENT_SECRET=your-client-secret-here

# Redirect URI (must match Azure app registration)
ENTRA_REDIRECT_URI=https://your-domain.com/auth/entra/callback

# Site URL for redirects after authentication
SITE_URL=https://your-domain.com

# Supabase Configuration (automatically provided by Supabase)
SUPABASE_URL=your-supabase-url
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key